import 'package:flutter/material.dart';
import 'package:swadesic/model/faq/faq_model.dart';
import 'package:swadesic/services/store_faq_services/store_faq_service.dart';

class StoreFaqDataModel with ChangeNotifier {
  List<StoreFaqCategory> faqCategories = [];
  bool isLoading = false;
  String? errorMessage;
  String? storeReference;
  final StoreFaqService _storeFaqService = StoreFaqService();

  // Constructor - initialize empty, data will be loaded via loadStoreFaqData()
  StoreFaqDataModel();

  // Get the Store FAQ categories
  List<StoreFaqCategory> get getStoreFaqCategories => faqCategories;

  // Get loading state
  bool get getIsLoading => isLoading;

  // Get error message
  String? get getErrorMessage => errorMessage;

  // Get store reference
  String? get getStoreReference => storeReference;

  // Set store reference
  void setStoreReference(String reference) {
    storeReference = reference;
    notifyListeners();
  }

  // Add Store FAQ categories
  void addStoreFaqCategories({required List<StoreFaqCategory> data}) {
    faqCategories = data;
    notifyListeners();
  }

  // Set loading state
  void setLoading(bool loading) {
    isLoading = loading;
    notifyListeners();
  }

  // Set error message
  void setError(String? error) {
    errorMessage = error;
    notifyListeners();
  }

  // Load Store FAQ data from service
  Future<void> loadStoreFaqData({required String storeReference}) async {
    try {
      setLoading(true);
      setError(null);
      this.storeReference = storeReference;

      final response = await _storeFaqService.getStoreFaqData(storeReference: storeReference);
      addStoreFaqCategories(data: response.faqCategories);
    } catch (e) {
      setError('Failed to load store FAQ data: ${e.toString()}');
    } finally {
      setLoading(false);
    }
  }

  // Refresh Store FAQ data
  Future<void> refreshStoreFaqData() async {
    if (storeReference != null) {
      await loadStoreFaqData(storeReference: storeReference!);
    }
  }

  // Add new FAQ item
  Future<StoreFaqItem?> addFaqItem({
    required String categoryName,
    required String question,
    required String answer,
  }) async {
    if (storeReference == null) return null;

    try {
      setLoading(true);
      setError(null);

      final newItem = await _storeFaqService.createFaqItem(
        storeReference: storeReference!,
        categoryName: categoryName,
        question: question,
        answer: answer,
      );

      // Refresh data to get updated categories and items
      await refreshStoreFaqData();
      
      return newItem;
    } catch (e) {
      setError('Failed to add FAQ item: ${e.toString()}');
      return null;
    } finally {
      setLoading(false);
    }
  }

  // Delete FAQ item
  Future<void> deleteFaqItem({
    required String itemKey,
  }) async {
    if (storeReference == null) return;

    try {
      setLoading(true);
      setError(null);

      await _storeFaqService.deleteFaqItem(
        storeReference: storeReference!,
        itemKey: itemKey,
      );

      // Refresh data to get updated categories and items
      await refreshStoreFaqData();
    } catch (e) {
      setError('Failed to delete FAQ item: ${e.toString()}');
    } finally {
      setLoading(false);
    }
  }

  // Reorder FAQ item
  Future<bool> reorderFaqItem({
    required String itemKey,
    required int newOrder,
  }) async {
    if (storeReference == null) return false;

    try {
      setLoading(true);
      setError(null);

      await _storeFaqService.reorderFaqItem(
        storeReference: storeReference!,
        itemKey: itemKey,
        newOrder: newOrder,
      );

      // Refresh data to get updated order
      await refreshStoreFaqData();
      
      return true;
    } catch (e) {
      setError('Failed to reorder FAQ item: ${e.toString()}');
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Switch FAQ items
  Future<bool> switchFaqItems({
    required String itemKey1,
    required String itemKey2,
  }) async {
    if (storeReference == null) return false;

    try {
      setLoading(true);
      setError(null);

      await _storeFaqService.switchFaqItems(
        storeReference: storeReference!,
        itemKey1: itemKey1,
        itemKey2: itemKey2,
      );

      // Refresh data to get updated order
      await refreshStoreFaqData();
      
      return true;
    } catch (e) {
      setError('Failed to switch FAQ items: ${e.toString()}');
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Update FAQ item locally (for UI updates before API call)
  void updateFaqItemLocally({
    required String categoryKey,
    required String itemKey,
    String? question,
    String? answer,
    bool? isExpanded,
  }) {
    final categoryIndex = faqCategories.indexWhere((cat) => cat.categoryKey == categoryKey);
    if (categoryIndex == -1) return;

    final itemIndex = faqCategories[categoryIndex].items.indexWhere((item) => item.itemKey == itemKey);
    if (itemIndex == -1) return;

    final updatedItem = faqCategories[categoryIndex].items[itemIndex].copyWith(
      question: question,
      answer: answer,
      isExpanded: isExpanded,
    );

    final updatedItems = List<StoreFaqItem>.from(faqCategories[categoryIndex].items);
    updatedItems[itemIndex] = updatedItem;

    final updatedCategory = faqCategories[categoryIndex].copyWith(items: updatedItems);
    final updatedCategories = List<StoreFaqCategory>.from(faqCategories);
    updatedCategories[categoryIndex] = updatedCategory;

    faqCategories = updatedCategories;
    notifyListeners();
  }

  // Toggle FAQ item expansion
  void toggleFaqItemExpansion({
    required String categoryKey,
    required String itemKey,
  }) {
    final categoryIndex = faqCategories.indexWhere((cat) => cat.categoryKey == categoryKey);
    if (categoryIndex == -1) return;

    final itemIndex = faqCategories[categoryIndex].items.indexWhere((item) => item.itemKey == itemKey);
    if (itemIndex == -1) return;

    final currentItem = faqCategories[categoryIndex].items[itemIndex];
    updateFaqItemLocally(
      categoryKey: categoryKey,
      itemKey: itemKey,
      isExpanded: !currentItem.isExpanded,
    );
  }

  // Clear all data
  void clearData() {
    faqCategories = [];
    isLoading = false;
    errorMessage = null;
    storeReference = null;
    notifyListeners();
  }

  // Get category by key
  StoreFaqCategory? getCategoryByKey(String categoryKey) {
    try {
      return faqCategories.firstWhere((cat) => cat.categoryKey == categoryKey);
    } catch (e) {
      return null;
    }
  }

  // Get item by keys
  StoreFaqItem? getItemByKeys(String categoryKey, String itemKey) {
    final category = getCategoryByKey(categoryKey);
    if (category == null) return null;

    try {
      return category.items.firstWhere((item) => item.itemKey == itemKey);
    } catch (e) {
      return null;
    }
  }

  // Check if store owner (based on store reference matching)
  bool isStoreOwner(String? currentStoreReference) {
    return storeReference != null && 
           currentStoreReference != null && 
           storeReference == currentStoreReference;
  }
}
