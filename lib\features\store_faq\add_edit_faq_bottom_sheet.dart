import 'package:flutter/material.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_constants.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_test_fields/app_text_fields.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/app_title_and_options/app_title_and_options.dart';
import 'package:swadesic/util/common_widgets.dart';

class AddEditFaqBottomSheet extends StatefulWidget {
  final bool isEdit;
  final String? initialCategoryName;
  final String? initialQuestion;
  final String? initialAnswer;
  final Function(String categoryName, String question, String answer) onSave;

  const AddEditFaqBottomSheet({
    Key? key,
    this.isEdit = false,
    this.initialCategoryName,
    this.initialQuestion,
    this.initialAnswer,
    required this.onSave,
  }) : super(key: key);

  @override
  State<AddEditFaqBottomSheet> createState() => _AddEditFaqBottomSheetState();
}

class _AddEditFaqBottomSheetState extends State<AddEditFaqBottomSheet> {
  late TextEditingController _categoryController;
  late TextEditingController _questionController;
  late TextEditingController _answerController;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _categoryController = TextEditingController(text: widget.initialCategoryName ?? '');
    _questionController = TextEditingController(text: widget.initialQuestion ?? '');
    _answerController = TextEditingController(text: widget.initialAnswer ?? '');
  }

  @override
  void dispose() {
    _categoryController.dispose();
    _questionController.dispose();
    _answerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: 20,
        right: 20,
        top: 20,
        bottom: MediaQuery.of(context).viewInsets.bottom + 20,
      ),
      decoration: const BoxDecoration(
        color: AppColors.appWhite,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: AppColors.borderColor1,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            verticalSizedBox(20),

            // Title using AppTitleAndOptions
            AppTitleAndOptions(
              title: widget.isEdit ? 'Edit FAQ' : 'Add an Option',
              titlePaddingHorizontal: 0,
            ),
            verticalSizedBox(24),

            // Category Name Field (only for new FAQ)
            // if (!widget.isEdit) ...[
            //   AppTitleAndOptions(
            //     title: 'Category',
            //     option: AppTextFields.allTextField(
            //       context: context,
            //       textEditingController: _categoryController,
            //       hintText: 'Enter category name',
            //     ),
            //   ),
            //   verticalSizedBox(16),
            // ],

            // Question Field
            AppTitleAndOptions(
              title: 'Question',
              option: AppTextFields.productNameTextField(
                context: context,
                textEditingController: _questionController,
                hintText: widget.initialQuestion ?? 'Enter your question',
                maxEntry: 200,
                maxLines: 2,
                minLines: 1,
              ),
            ),
            verticalSizedBox(16),

            // Answer Field
            AppTitleAndOptions(
              title: 'Answer',
              option: AppTextFields.allTextField(
                context: context,
                textEditingController: _answerController,
                hintText: 'Enter the answer',
                maxEntry: 1000,
                maxLines: 10,
                minLines: 5,
                keyboardType: TextInputType.multiline,
                textInputAction: TextInputAction.newline,
            ),
            ),
            const SizedBox(height: 24),

            // Add Button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _handleSave,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.appBlack,
                  foregroundColor: AppColors.appWhite,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  widget.isEdit ? 'Update' : 'Add',
                  style: AppTextStyle.access0(textColor: AppColors.appWhite),
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _handleSave() {
    if (_formKey.currentState?.validate() ?? false) {
      final categoryName = widget.isEdit 
          ? (widget.initialCategoryName ?? 'General')
          : _categoryController.text.trim();
      final question = _questionController.text.trim();
      final answer = _answerController.text.trim();

      widget.onSave(categoryName, question, answer);
    }
  }
}
