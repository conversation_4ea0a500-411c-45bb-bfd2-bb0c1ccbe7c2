import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:swadesic/util/app_colors.dart';
import 'package:swadesic/util/app_images.dart';
import 'package:swadesic/util/app_strings.dart';
import 'package:swadesic/util/app_text_style.dart';
import 'package:swadesic/util/common_widgets.dart';

class NoResult extends StatefulWidget {
  final String message;
  final bool showIcon;
  const NoResult({super.key, required this.message, this.showIcon = true});

  @override
  State<NoResult> createState() => _NoResultState();
}

class _NoResultState extends State<NoResult> {
  @override
  Widget build(BuildContext context) {
    return noResults();
  }

  //region No results
  Widget noResults() {
    return Center(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            widget.showIcon ? SvgPicture.asset(AppImages.noSearchResultIcon) : Container(),
            verticalSizedBox(7.12),
            Text(
              widget.message,
              textAlign: TextAlign.center,
              style: AppTextStyle.contentText0(textColor: AppColors.appBlack),
            ),
          ],
        ));
  }
}
